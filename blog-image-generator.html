<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Image Generator - CalculatorSuites</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: #f5f5f5;
            padding: 20px;
        }

        .image-container {
            width: 400px;
            height: 250px;
            margin: 20px;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            display: inline-block;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .image-content {
            width: 100%;
            height: 100%;
            padding: 30px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            color: white;
        }

        .category {
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .title {
            font-size: 20px;
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: auto;
        }

        .brand {
            font-size: 14px;
            font-weight: 500;
            opacity: 0.8;
            align-self: flex-end;
        }

        .icon {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            opacity: 0.3;
        }

        /* Category-specific backgrounds */
        .investment {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .tax {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .loan {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .calculator {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .health {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .business {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
        }

        .business .category,
        .business .title,
        .business .brand {
            color: #333;
        }

        /* Decorative elements */
        .image-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="80" cy="20" r="2" fill="white" opacity="0.3"/><circle cx="90" cy="40" r="1.5" fill="white" opacity="0.2"/><circle cx="85" cy="60" r="1" fill="white" opacity="0.4"/></svg>');
            pointer-events: none;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .controls h2 {
            margin-bottom: 15px;
            color: #333;
        }

        .controls button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-family: 'Poppins', sans-serif;
            font-weight: 500;
        }

        .controls button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="controls">
        <h2>Blog Image Generator - CalculatorSuites</h2>
        <p>Click on any image to download it. All images use Poppins font and are optimized for blog cards.</p>
        <button onclick="downloadAll()">Download All Images</button>
    </div>

    <!-- GST Business Guide -->
    <div class="image-container" id="gst-business-guide">
        <div class="image-content tax">
            <div>
                <div class="category">Tax Planning</div>
                <div class="title">GST Guide for Small Business</div>
            </div>
            <div class="brand">CalculatorSuites</div>
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
            </svg>
        </div>
    </div>

    <!-- Home Loan Guide -->
    <div class="image-container" id="home-loan-guide">
        <div class="image-content loan">
            <div>
                <div class="category">Loan Planning</div>
                <div class="title">Home Loan EMI Planning Guide</div>
            </div>
            <div class="brand">CalculatorSuites</div>
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </svg>
        </div>
    </div>

    <!-- Calculator Selection Guide -->
    <div class="image-container" id="calculator-selection-guide">
        <div class="image-content calculator">
            <div>
                <div class="category">Calculator Guides</div>
                <div class="title">How to Choose the Right Calculator</div>
            </div>
            <div class="brand">CalculatorSuites</div>
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7.5 6c.83 0 1.5-.67 1.5-1.5S12.33 6 11.5 6 10 6.67 10 7.5 10.67 9 11.5 9zM16 17H8v-.5c0-1.17.83-2.17 2-2.45V13h4v1.05c1.17.28 2 1.28 2 2.45V17z"/>
            </svg>
        </div>
    </div>

    <!-- BMI Health Guide -->
    <div class="image-container" id="bmi-health-guide">
        <div class="image-content health">
            <div>
                <div class="category">Health & Wellness</div>
                <div class="title">Understanding BMI & Health Assessment</div>
            </div>
            <div class="brand">CalculatorSuites</div>
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
        </div>
    </div>

    <!-- Tax Planning Strategies -->
    <div class="image-container" id="tax-planning-strategies">
        <div class="image-content tax">
            <div>
                <div class="category">Tax Planning</div>
                <div class="title">Tax Planning Strategies 2024</div>
            </div>
            <div class="brand">CalculatorSuites</div>
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
            </svg>
        </div>
    </div>

    <!-- SIP Tenure Guide -->
    <div class="image-container" id="sip-tenure-guide">
        <div class="image-content investment">
            <div>
                <div class="category">Investment Planning</div>
                <div class="title">How to Choose SIP Tenure India</div>
            </div>
            <div class="brand">CalculatorSuites</div>
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
            </svg>
        </div>
    </div>

    <!-- GST Filing Mistakes -->
    <div class="image-container" id="gst-filing-mistakes">
        <div class="image-content business">
            <div>
                <div class="category">Tax Planning</div>
                <div class="title">Common GST Filing Mistakes</div>
            </div>
            <div class="brand">CalculatorSuites</div>
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
            </svg>
        </div>
    </div>

    <script>
        // Function to convert div to canvas and download
        function downloadImage(elementId, filename) {
            const element = document.getElementById(elementId);
            
            // Use html2canvas library (you'll need to include it)
            html2canvas(element, {
                width: 400,
                height: 250,
                scale: 2, // Higher resolution
                backgroundColor: null
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        }

        // Add click handlers to all images
        document.addEventListener('DOMContentLoaded', function() {
            const images = [
                { id: 'gst-business-guide', filename: 'gst-business-guide.jpg' },
                { id: 'home-loan-guide', filename: 'home-loan-guide.jpg' },
                { id: 'calculator-selection-guide', filename: 'calculator-selection-guide.jpg' },
                { id: 'bmi-health-guide', filename: 'bmi-health-guide.jpg' },
                { id: 'tax-planning-strategies', filename: 'tax-planning-strategies.jpg' },
                { id: 'sip-tenure-guide', filename: 'how-to-choose-sip-tenure-india.jpg' },
                { id: 'gst-filing-mistakes', filename: 'common-gst-filing-mistakes-small-business-india.jpg' }
            ];

            images.forEach(img => {
                document.getElementById(img.id).addEventListener('click', () => {
                    downloadImage(img.id, img.filename);
                });
            });
        });

        function downloadAll() {
            const images = [
                { id: 'gst-business-guide', filename: 'gst-business-guide.jpg' },
                { id: 'home-loan-guide', filename: 'home-loan-guide.jpg' },
                { id: 'calculator-selection-guide', filename: 'calculator-selection-guide.jpg' },
                { id: 'bmi-health-guide', filename: 'bmi-health-guide.jpg' },
                { id: 'tax-planning-strategies', filename: 'tax-planning-strategies.jpg' },
                { id: 'sip-tenure-guide', filename: 'how-to-choose-sip-tenure-india.jpg' },
                { id: 'gst-filing-mistakes', filename: 'common-gst-filing-mistakes-small-business-india.jpg' }
            ];

            images.forEach((img, index) => {
                setTimeout(() => {
                    downloadImage(img.id, img.filename);
                }, index * 1000); // Delay each download by 1 second
            });
        }
    </script>

    <!-- Include html2canvas library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
</body>
</html>
