<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG to JPG Converter</title>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .converter {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .preview {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .image-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        .image-item img {
            display: block;
            width: 400px;
            height: 250px;
        }
        .image-item .info {
            padding: 10px;
            text-align: center;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-family: 'Poppins', sans-serif;
            font-weight: 500;
        }
        button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <div class="converter">
        <h1>SVG to JPG Converter for Blog Images</h1>
        <p>This tool converts the SVG blog images to JPG format with proper compression.</p>
        <button onclick="convertAllImages()">Convert All SVGs to JPG</button>
        <button onclick="downloadAllJPGs()">Download All JPGs</button>
    </div>

    <div class="preview" id="preview">
        <!-- Images will be loaded here -->
    </div>

    <script>
        const svgFiles = [
            'gst-business-guide.svg',
            'home-loan-guide.svg',
            'calculator-selection-guide.svg',
            'bmi-health-guide.svg',
            'tax-planning-strategies.svg',
            'how-to-choose-sip-tenure-india.svg',
            'common-gst-filing-mistakes-small-business-india.svg'
        ];

        let convertedImages = {};

        function loadSVGImages() {
            const preview = document.getElementById('preview');
            
            svgFiles.forEach(filename => {
                const imageItem = document.createElement('div');
                imageItem.className = 'image-item';
                
                const img = document.createElement('img');
                img.src = `assets/images/blog/${filename}`;
                img.alt = filename;
                
                const info = document.createElement('div');
                info.className = 'info';
                info.innerHTML = `
                    <strong>${filename}</strong><br>
                    <button onclick="convertSingleImage('${filename}')">Convert to JPG</button>
                    <button onclick="downloadJPG('${filename}')" id="download-${filename}" style="display:none;">Download JPG</button>
                `;
                
                imageItem.appendChild(img);
                imageItem.appendChild(info);
                preview.appendChild(imageItem);
            });
        }

        function convertSVGToJPG(svgUrl, filename) {
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = function() {
                    const canvas = document.createElement('canvas');
                    canvas.width = 400;
                    canvas.height = 250;
                    
                    const ctx = canvas.getContext('2d');
                    
                    // Fill with white background
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // Draw the SVG
                    ctx.drawImage(img, 0, 0, 400, 250);
                    
                    // Convert to JPG
                    const jpgDataUrl = canvas.toDataURL('image/jpeg', 0.9);
                    convertedImages[filename] = jpgDataUrl;
                    
                    resolve(jpgDataUrl);
                };
                
                img.src = svgUrl;
            });
        }

        async function convertSingleImage(filename) {
            try {
                const svgUrl = `assets/images/blog/${filename}`;
                await convertSVGToJPG(svgUrl, filename);
                
                // Show download button
                const downloadBtn = document.getElementById(`download-${filename}`);
                downloadBtn.style.display = 'inline-block';
                
                console.log(`Converted ${filename} to JPG`);
            } catch (error) {
                console.error(`Error converting ${filename}:`, error);
            }
        }

        async function convertAllImages() {
            for (const filename of svgFiles) {
                await convertSingleImage(filename);
            }
            
            // Show download all button
            document.getElementById('downloadAllBtn').style.display = 'inline-block';
        }

        function downloadJPG(filename) {
            const jpgFilename = filename.replace('.svg', '.jpg');
            const jpgDataUrl = convertedImages[filename];
            
            if (jpgDataUrl) {
                const link = document.createElement('a');
                link.download = jpgFilename;
                link.href = jpgDataUrl;
                link.click();
            }
        }

        function downloadAllJPGs() {
            svgFiles.forEach(filename => {
                if (convertedImages[filename]) {
                    setTimeout(() => downloadJPG(filename), 100);
                }
            });
        }

        // Load images when page loads
        document.addEventListener('DOMContentLoaded', loadSVGImages);
    </script>
</body>
</html>
