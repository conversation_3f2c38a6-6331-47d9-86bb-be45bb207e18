// Node.js script to convert SVG images to JPG format
// Run with: node create-jpg-images.js

const fs = require('fs');
const path = require('path');

// SVG to JPG conversion using sharp (install with: npm install sharp)
const sharp = require('sharp');

const svgFiles = [
    'gst-business-guide.svg',
    'home-loan-guide.svg',
    'calculator-selection-guide.svg',
    'bmi-health-guide.svg',
    'tax-planning-strategies.svg',
    'how-to-choose-sip-tenure-india.svg',
    'common-gst-filing-mistakes-small-business-india.svg'
];

const inputDir = './assets/images/blog/';
const outputDir = './assets/images/blog/';

async function convertSVGToJPG() {
    console.log('Converting SVG images to JPG format...');
    
    for (const svgFile of svgFiles) {
        const inputPath = path.join(inputDir, svgFile);
        const outputPath = path.join(outputDir, svgFile.replace('.svg', '.jpg'));
        
        try {
            await sharp(inputPath)
                .jpeg({ quality: 90 })
                .resize(400, 250)
                .toFile(outputPath);
            
            console.log(`✓ Converted ${svgFile} to ${svgFile.replace('.svg', '.jpg')}`);
        } catch (error) {
            console.error(`✗ Error converting ${svgFile}:`, error.message);
        }
    }
    
    console.log('Conversion complete!');
}

// Check if sharp is installed
try {
    require('sharp');
    convertSVGToJPG();
} catch (error) {
    console.log('Sharp library not found. Please install it with:');
    console.log('npm install sharp');
    console.log('');
    console.log('Alternative: Use the blog-image-generator.html file to download images manually.');
}
